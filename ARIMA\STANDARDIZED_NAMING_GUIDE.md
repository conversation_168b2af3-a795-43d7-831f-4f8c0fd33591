# ARIMA月度预测器标准化文件命名规范

## 概述

为了提高文件管理的效率和一致性，ARIMA月度预测器现在实施了标准化的文件命名规范。所有分析过程中生成的输出文件都将遵循统一的命名格式。

## 命名格式

**标准格式**：`{文件类型}-{用电类型}-{模型名称}-{外部变量标识}-{日期时间戳}.{扩展名}`

### 各部分说明

#### 1. 文件类型
- `prediction` - 预测结果文件
- `evaluation` - 模型评估结果
- `plot-series_analysis` - 序列分析图表
- `plot-seasonal_decomposition` - 季节性分解图表
- `plot-forecast` - 预测结果图表
- `plot-evaluation` - 评估结果图表
- `diagnostics` - 模型诊断结果图表
- `model` - 模型保存文件（如需要）

#### 2. 用电类型
- `proxy_purchase` - 代理购电
- `residential` - 居民用电
- `agricultural` - 农业用电

#### 3. 模型名称
- `ARIMA` - 自回归积分滑动平均模型
- `SARIMAX` - 季节性ARIMA模型（含外部回归变量支持）

#### 4. 外部变量标识
- `with_exog` - 使用外部回归变量
- `no_exog` - 未使用外部回归变量

#### 5. 日期时间戳
- 格式：`YYYYMMDD_HHMMSS`
- 示例：`20250729_143022`（2025年7月29日 14:30:22）

## 文件名示例

### 预测结果文件
```
prediction-proxy_purchase-SARIMAX-with_exog-20250729_143022.csv
prediction-residential-ARIMA-no_exog-20250729_143022.csv
prediction-agricultural-SARIMAX-no_exog-20250729_143022.csv
```

### 评估结果文件
```
evaluation-proxy_purchase-SARIMAX-with_exog-20250729_143022.csv
evaluation-residential-ARIMA-no_exog-20250729_143022.csv
```

### 图表文件
```
plot-series_analysis-proxy_purchase-SARIMAX-with_exog-20250729_143022.png
plot-seasonal_decomposition-residential-ARIMA-no_exog-20250729_143022.png
plot-forecast-agricultural-SARIMAX-no_exog-20250729_143022.png
plot-evaluation-proxy_purchase-SARIMAX-with_exog-20250729_143022.png
```

### 诊断结果文件
```
diagnostics-proxy_purchase-SARIMAX-with_exog-20250729_143022.png
diagnostics-residential-ARIMA-no_exog-20250729_143022.png
```

## 实施细节

### 代码实现

新增了两个核心方法：

1. **`_generate_filename(file_type, extension)`**
   - 生成符合标准的文件名
   - 自动处理用电类型映射
   - 自动检测外部变量使用情况
   - 生成时间戳

2. **`_save_file_with_standard_name(data, file_type, extension, subdir)`**
   - 使用标准命名保存文件
   - 支持多种数据类型（DataFrame、图表等）
   - 自动创建目录结构
   - 提供保存确认信息

### 修改的保存操作

所有原有的文件保存操作都已更新：

- ✅ 序列分析图保存
- ✅ 季节性分解图保存
- ✅ 模型诊断图保存
- ✅ 预测结果CSV保存
- ✅ 预测图表保存
- ✅ 评估结果CSV保存
- ✅ 评估图表保存

## 优势

### 1. 文件识别性
- 从文件名即可快速识别内容类型
- 清晰区分不同用电类型和模型配置
- 时间戳确保文件唯一性

### 2. 文件组织性
- 相同类型文件自然聚集
- 便于批量处理和分析
- 支持自动化脚本处理

### 3. 可追溯性
- 精确的时间戳记录
- 模型配置信息嵌入文件名
- 便于结果复现和验证

### 4. 兼容性
- 文件名不含特殊字符
- 跨平台兼容
- 便于版本控制系统管理

## 使用示例

### 基本使用
```python
# 创建预测器
predictor = ARIMAMonthlyPredictor(
    target_column='代理购电',
    model_type='SARIMAX',
    use_external_regressors=True
)

# 运行分析（自动使用标准命名）
predictions = predictor.run_complete_analysis(prediction_months=12)

# 生成的文件将自动使用标准命名格式
```

### 手动保存示例
```python
# 手动使用标准命名保存数据
filepath = predictor._save_file_with_standard_name(
    data=my_dataframe,
    file_type='prediction',
    extension='csv'
)

# 生成自定义文件名
filename = predictor._generate_filename('evaluation', 'csv')
```

## 测试验证

运行测试脚本验证命名规范：
```bash
cd ARIMA
python test_standardized_naming.py
```

测试将验证：
- 文件名格式正确性
- 各组件映射准确性
- 时间戳格式有效性
- 实际文件生成情况

## 迁移说明

### 对现有用户的影响
- 新生成的文件将使用新命名格式
- 现有文件不受影响
- 代码向后兼容

### 建议操作
1. 运行测试脚本验证新格式
2. 更新任何依赖特定文件名的脚本
3. 考虑重新组织现有结果文件

## 总结

标准化文件命名规范显著提升了ARIMA月度预测器的文件管理能力，使得：
- 文件更易识别和组织
- 分析结果更具可追溯性
- 自动化处理更加便利
- 团队协作更加高效

这一改进为后续的批量分析、结果比较和自动化报告生成奠定了坚实基础。
