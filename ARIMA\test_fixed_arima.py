"""
测试修复后的ARIMA月度预测器
验证评估和预测使用相同的模型参数
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from arima_monthly_predictor import ARIMAMonthlyPredictor


def test_parameter_consistency():
    """测试参数一致性：验证评估和预测使用相同的模型参数"""
    
    print("=" * 80)
    print("测试ARIMA预测器参数一致性修复")
    print("=" * 80)
    
    # 测试所有三种电力类型
    target_columns = ['代理购电', '居民', '农业']
    
    for target_column in target_columns:
        print(f"\n{'='*60}")
        print(f"测试目标: {target_column}")
        print(f"{'='*60}")
        
        try:
            # 创建预测器
            predictor = ARIMAMonthlyPredictor(
                data_path="./data/居民、农业2019-.xlsx",
                target_column=target_column,
                model_type="SARIMAX",
                use_external_regressors=True,
                correlation_threshold=0.3,
                p_value_threshold=0.05,
                default_data_path="./data/居民、农业2019-.xlsx",
                correlation_results_pattern='feature_analysis_results/result/monthly_weather_electricity_correlations_*.csv',
                monthly_data_patterns=['./data/整合数据2025_7.xlsx', './data/整合天气数据_代理购电_20250717_171422.xlsx'],
                weather_data_paths=['data/丽水市_月度统计_201901_202508.csv']
            )
            
            # 运行完整分析（修复后的版本）
            print(f"\n开始分析 {target_column}...")
            predictions = predictor.run_complete_analysis(prediction_months=6, evaluation=True)
            
            # 验证参数一致性
            print(f"\n{'*'*50}")
            print("参数一致性验证结果:")
            print(f"{'*'*50}")
            
            if predictor.optimal_order is not None:
                print(f"✓ 成功：验证阶段确定的最优参数已保存")
                print(f"  ARIMA参数: {predictor.optimal_order}")
                if predictor.optimal_seasonal_order:
                    print(f"  季节性参数: {predictor.optimal_seasonal_order}")
                
                # 检查最终模型是否使用了相同参数
                if predictor.fitted_model is not None:
                    final_order = predictor.fitted_model.model.order
                    final_seasonal = getattr(predictor.fitted_model.model, 'seasonal_order', None)
                    
                    if final_order == predictor.optimal_order:
                        print(f"✓ 成功：最终预测模型使用了相同的ARIMA参数")
                    else:
                        print(f"✗ 错误：参数不一致！验证参数={predictor.optimal_order}, 最终参数={final_order}")
                    
                    if final_seasonal and predictor.optimal_seasonal_order:
                        if final_seasonal == predictor.optimal_seasonal_order:
                            print(f"✓ 成功：最终预测模型使用了相同的季节性参数")
                        else:
                            print(f"✗ 错误：季节性参数不一致！验证参数={predictor.optimal_seasonal_order}, 最终参数={final_seasonal}")
                
                print(f"✓ 修复成功：{target_column} 的验证和预测模型使用相同参数")
            else:
                print(f"⚠ 警告：{target_column} 未能确定最优参数（可能数据不足或其他问题）")
            
            # 显示预测结果摘要
            if predictions is not None:
                print(f"\n预测结果摘要:")
                print(f"预测期间: {predictions.index[0].strftime('%Y-%m')} 到 {predictions.index[-1].strftime('%Y-%m')}")
                print(f"平均预测值: {predictions['预测值'].mean():.2f}")
                print(f"预测值范围: {predictions['预测值'].min():.2f} - {predictions['预测值'].max():.2f}")
            
        except Exception as e:
            print(f"✗ 错误：{target_column} 分析失败: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n{'='*80}")
    print("参数一致性测试完成")
    print("修复说明:")
    print("1. 现在先进行模型评估以确定最优参数")
    print("2. 然后使用相同的最优参数拟合最终预测模型")
    print("3. 这确保了验证结果和实际预测的模型一致性")
    print("4. 提高了模型的可解释性和泛化能力")
    print(f"{'='*80}")


if __name__ == "__main__":
    test_parameter_consistency()
