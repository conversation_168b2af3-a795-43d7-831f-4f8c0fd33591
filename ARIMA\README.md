# 时间序列月度电力预测模型

本模块提供基于ARIMA（自回归积分滑动平均）和SARIMAX（季节性ARIMA）模型的月度电力数据预测功能，支持对代理购电、居民用电、农业用电等数据进行时间序列预测。

## 功能特点

- **双模型支持**: 支持ARIMA和SARIMAX两种时间序列模型
- **自动参数选择**: 通过网格搜索自动选择最优模型参数
- **季节性处理**: SARIMAX模型能够处理季节性数据
- **数据预处理**: 支持CSV和Excel格式，自动处理月度数据
- **平稳性检验**: ADF检验确保时间序列平稳性
- **季节性分解**: 分析时间序列的趋势、季节性和残差成分
- **模型诊断**: 残差分析、Ljung-Box检验等
- **模型评估**: 使用历史数据进行回测评估
- **可视化输出**: 生成预测图表、诊断图等
- **多目标支持**: 支持预测多种电力消费类型

## 安装依赖

```bash
pip install -r requirements.txt
```

主要依赖包：
- pandas: 数据处理
- numpy: 数值计算
- matplotlib: 图表绘制
- statsmodels: ARIMA模型
- scikit-learn: 评估指标
- scipy: 统计分析

## 快速开始

### 1. 基本使用

```python
from arima_monthly_predictor import ARIMAMonthlyPredictor

# 创建ARIMA预测器
arima_predictor = ARIMAMonthlyPredictor(
    target_column='代理购电',
    model_type='ARIMA'
)

# 创建SARIMAX预测器（适合季节性数据）
sarimax_predictor = ARIMAMonthlyPredictor(
    target_column='代理购电',
    model_type='SARIMAX'
)

# 运行完整分析（包含数据分析、模型拟合、预测、可视化）
predictions = arima_predictor.run_complete_analysis(prediction_months=12)
```

### 2. 交互式使用

```bash
python arima_monthly_predictor.py
```

程序会提示选择预测目标、模型类型和预测月数。

### 3. 命令行快速使用

```bash
# 使用ARIMA模型
python run_arima_prediction.py --target 代理购电 --months 12 --model ARIMA

# 使用SARIMAX模型
python run_arima_prediction.py --target 代理购电 --months 12 --model SARIMAX
```

### 4. 使用示例

```bash
python example_usage.py
```

提供多种使用示例，包括ARIMA和SARIMAX模型的对比。

## 详细使用说明

### 数据格式要求

支持两种数据格式：

**1. CSV格式（日度数据）**：
- `日期`: 日期列（YYYY-MM-DD格式）
- `代理购电`/`居民`/`农业`: 目标预测列
- 其他列: 可选的特征列

**2. Excel格式（月度数据）**：
- 工作表名: `月数据`
- `月份`: 月份列（YYYYMM格式）
- `代理购电`/`居民`/`农业`: 目标预测列

### 主要类和方法

#### ARIMAMonthlyPredictor类

**初始化参数:**
- `data_path`: 数据文件路径
- `target_column`: 目标预测列名（默认'代理购电'）
- `model_type`: 模型类型，'ARIMA' 或 'SARIMAX'（默认'ARIMA'）

**主要方法:**

1. `load_and_prepare_data()`: 加载数据并转换为月度数据
2. `check_stationarity()`: 平稳性检验
3. `seasonal_decomposition()`: 季节性分解
4. `fit_model()`: 拟合ARIMA或SARIMAX模型
5. `predict_future()`: 预测未来数据
6. `evaluate_model()`: 模型评估
7. `plot_predictions()`: 绘制预测结果
8. `run_complete_analysis()`: 运行完整分析流程

### 输出文件

所有结果保存在`ARIMA/results/`目录下，采用标准化命名规范：

**命名格式**: `{文件类型}-{用电类型}-{模型名称}-{外部变量标识}-{日期时间戳}.{扩展名}`

**文件类型示例**:
- **预测结果**: `prediction-proxy_purchase-SARIMAX-with_exog-20250729_143022.csv`
- **预测图表**: `plot-forecast-residential-ARIMA-no_exog-20250729_143022.png`
- **序列分析图**: `plot-series_analysis-agricultural-SARIMAX-no_exog-20250729_143022.png`
- **季节性分解图**: `plot-seasonal_decomposition-proxy_purchase-SARIMAX-with_exog-20250729_143022.png`
- **模型诊断图**: `diagnostics-residential-ARIMA-no_exog-20250729_143022.png`
- **评估结果**: `evaluation-agricultural-SARIMAX-no_exog-20250729_143022.csv`
- **评估图表**: `plot-evaluation-proxy_purchase-SARIMAX-with_exog-20250729_143022.png`

**命名组件说明**:
- 用电类型: `proxy_purchase`(代理购电), `residential`(居民), `agricultural`(农业)
- 模型名称: `ARIMA`, `SARIMAX`
- 外部变量: `with_exog`(使用), `no_exog`(未使用)
- 时间戳: `YYYYMMDD_HHMMSS`格式

详细说明请参考: [标准化命名规范指南](STANDARDIZED_NAMING_GUIDE.md)

## 高级用法

### 自定义ARIMA参数

```python
# 手动指定ARIMA参数
predictor.fit_arima_model(order=(2, 1, 2))
```

### 模型评估

```python
# 使用最后6个月作为测试集进行评估
eval_results = predictor.evaluate_model(test_size=6)
```

### 自定义置信区间

```python
# 设置95%置信区间进行预测
predictions = predictor.predict_future(periods=12, confidence_level=0.95)
```

## 模型说明

### ARIMA模型

ARIMA(p,d,q)模型包含三个参数：
- **p**: 自回归项数
- **d**: 差分次数
- **q**: 滑动平均项数

### SARIMAX模型

SARIMAX(p,d,q)(P,D,Q,m)模型在ARIMA基础上增加季节性参数：
- **(p,d,q)**: 非季节性ARIMA参数
- **(P,D,Q,m)**: 季节性参数，其中m为季节周期（月度数据通常为12）

### 自动参数选择

程序通过网格搜索选择最优参数：

**ARIMA模型**：
- p: 0-5, d: 0-2, q: 0-5

**SARIMAX模型**：
- 非季节性: p: 0-5, d: 0-2, q: 0-5
- 季节性: P: 0-2, D: 0-1, Q: 0-2, m: 12

选择标准为AIC（赤池信息准则）最小。

### 评估指标

- **MAE**: 平均绝对误差
- **MSE**: 均方误差
- **RMSE**: 均方根误差
- **MAPE**: 平均绝对百分比误差

## 注意事项

1. **数据质量**: 确保输入数据完整，无大量缺失值
2. **数据长度**: 建议至少有24个月的历史数据以获得可靠预测
3. **季节性**: 对于具有明显季节性的数据，考虑使用SARIMA模型
4. **平稳性**: 非平稳序列会自动进行差分处理
5. **异常值**: 建议预处理时处理明显的异常值

## 故障排除

### 常见问题

1. **导入错误**: 确保安装了所有依赖包
2. **数据格式错误**: 检查CSV文件格式和列名
3. **内存不足**: 对于大数据集，考虑减少历史数据长度
4. **收敛问题**: 尝试不同的ARIMA参数组合

### 性能优化

- 对于大数据集，可以限制自动参数搜索范围
- 使用`show_history_months`参数限制可视化的历史数据长度
- 定期清理results目录中的旧文件

## 扩展功能

可以基于现有框架扩展以下功能：
- SARIMA季节性模型
- 多变量VAR模型
- 外生变量ARIMAX模型
- 集成学习方法

## 联系支持

如有问题或建议，请联系开发团队。
