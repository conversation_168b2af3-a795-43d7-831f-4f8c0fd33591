"""
ARIMA月级别电力数据预测模型
支持对代理购电、居民用电、农业用电等数据进行月度预测
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
import os
import sys
import glob
from typing import List, Dict, Tuple, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

warnings.filterwarnings('ignore')

try:
    from statsmodels.tsa.arima.model import ARIMA
    from statsmodels.tsa.statespace.sarimax import SARIMAX
    from statsmodels.tsa.stattools import adfuller
    from statsmodels.tsa.seasonal import seasonal_decompose
    from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
    from statsmodels.stats.diagnostic import acorr_ljungbox
except ImportError:
    print("警告: statsmodels未安装，请运行: pip install statsmodels")
    sys.exit(1)


class CorrelationFeatureSelector:
    """基于相关性分析结果的特征选择器"""

    def __init__(self, correlation_threshold: float = 0.3, p_value_threshold: float = 0.05,
                 correlation_results_pattern: Optional[str] = None,
                 monthly_data_patterns: Optional[List[str]] = None):
        """
        初始化特征选择器

        Args:
            correlation_threshold: 相关系数阈值（绝对值）
            p_value_threshold: p值阈值
            correlation_results_pattern: 相关性分析结果文件路径模式，如果为None则使用默认模式
            monthly_data_patterns: 月度数据文件路径模式列表，如果为None则使用默认模式
        """
        self.correlation_threshold = correlation_threshold
        self.p_value_threshold = p_value_threshold
        self.correlation_data = None
        self.monthly_data = None

        # 配置文件路径模式
        self.correlation_results_pattern = (
            correlation_results_pattern or
            "feature_analysis_results/result/monthly_weather_electricity_correlations_*.csv"
        )

        self.monthly_data_patterns = monthly_data_patterns or [
            "./data/整合数据2025_7.xlsx",
            "./data/整合天气数据_代理购电_*.xlsx",
            "../data/整合天气数据_代理购电_*.xlsx",
            "data/整合天气数据_代理购电_*.xlsx",
            "feature_analysis_results/monthly_aggregated_data_*.csv"  # 向后兼容
        ]

    def load_correlation_results(self, correlation_file: Optional[str] = None) -> bool:
        """
        加载相关性分析结果

        Args:
            correlation_file: 相关性分析结果文件路径，如果为None则自动查找最新文件

        Returns:
            bool: 是否成功加载
        """
        try:
            if correlation_file is None:
                # 使用配置的相关性分析结果文件模式
                files = glob.glob(self.correlation_results_pattern)
                if not files:
                    print(f"未找到相关性分析结果文件，搜索模式: {self.correlation_results_pattern}")
                    return False
                correlation_file = max(files, key=os.path.getmtime)
                print(f"自动选择相关性分析文件: {correlation_file}")

            self.correlation_data = pd.read_csv(correlation_file)
            print(f"成功加载相关性分析结果，共{len(self.correlation_data)}条记录")
            return True

        except Exception as e:
            print(f"加载相关性分析结果失败: {e}")
            return False

    def load_monthly_data(self, monthly_file: Optional[str] = None) -> bool:
        """
        加载月度聚合数据

        Args:
            monthly_file: 月度数据文件路径，如果为None则自动查找最新文件

        Returns:
            bool: 是否成功加载
        """
        try:
            if monthly_file is None:
                # 使用配置的月度数据文件模式
                files = []
                for pattern in self.monthly_data_patterns:
                    pattern_files = glob.glob(pattern)
                    files.extend(pattern_files)
                    if pattern_files:
                        print(f"找到文件模式 {pattern}: {len(pattern_files)} 个文件")

                if not files:
                    print("未找到月度聚合数据文件")
                    print("尝试的模式:")
                    for pattern in self.monthly_data_patterns:
                        print(f"  - {pattern}")
                    return False

                monthly_file = max(files, key=os.path.getmtime)
                print(f"自动选择月度数据文件: {monthly_file}")

            # 根据文件扩展名选择读取方法
            if monthly_file.endswith('.xlsx'):
                self.monthly_data = pd.read_excel(monthly_file)
            else:
                self.monthly_data = pd.read_csv(monthly_file)

            # 处理日期列
            if '日期' in self.monthly_data.columns:
                # 如果已经有日期列，直接使用
                self.monthly_data['日期'] = pd.to_datetime(self.monthly_data['日期'])
            elif '年月' in self.monthly_data.columns:
                # 如果有年月列，转换为日期
                self.monthly_data['日期'] = pd.to_datetime(self.monthly_data['年月'], format='%Y-%m')
            elif '年月_str' in self.monthly_data.columns:
                # CSV文件格式
                self.monthly_data['日期'] = pd.to_datetime(self.monthly_data['年月_str'], format='%Y-%m')
            else:
                print("数据文件中缺少日期信息（'日期'、'年月'或'年月_str'列）")
                print(f"可用列: {list(self.monthly_data.columns)}")
                return False

            self.monthly_data.set_index('日期', inplace=True)
            print(f"成功加载月度数据，共{len(self.monthly_data)}个月")
            return True

        except Exception as e:
            print(f"加载月度数据失败: {e}")
            return False

    def get_selected_features(self, target_column: str) -> Tuple[List[str], Dict[str, float]]:
        """
        根据相关性分析结果选择特征并计算权重

        Args:
            target_column: 目标电力类型（居民、农业、代理购电）

        Returns:
            Tuple[List[str], Dict[str, float]]: (选中的特征列表, 特征权重字典)
        """
        if self.correlation_data is None:
            print("相关性数据未加载")
            return [], {}

        # 创建目标列名映射，处理不同的命名方式
        target_mapping = {
            '代理购电': ['代理购电', '电网代购用户', '代购用户'],
            '居民': ['居民', '居民用电'],
            '农业': ['农业', '农业用电']
        }

        # 获取可能的目标列名
        possible_targets = target_mapping.get(target_column, [target_column])

        # 尝试找到匹配的目标数据
        target_data = None
        matched_target = None
        for possible_target in possible_targets:
            temp_data = self.correlation_data[
                self.correlation_data['electricity_type'] == possible_target
            ]
            if len(temp_data) > 0:
                target_data = temp_data
                matched_target = possible_target
                break

        if target_data is None or len(target_data) == 0:
            print(f"未找到{target_column}的相关性数据")
            print(f"尝试的目标名称: {possible_targets}")
            print(f"可用的电力类型: {list(self.correlation_data['electricity_type'].unique())}")
            return [], {}

        print(f"找到匹配的目标类型: {matched_target} (原始: {target_column})")

        # 应用筛选条件 - 使用相关系数阈值和p值阈值
        # 首先尝试使用严格条件（需要显著性）
        selected_features = target_data[
            (target_data['pearson_abs_corr'] >= self.correlation_threshold) &
            (target_data['pearson_p_value'] <= self.p_value_threshold) &
            (target_data['pearson_significant'] == True)
        ]

        # 如果严格条件没有找到特征，尝试放宽条件（不要求显著性）
        if len(selected_features) == 0:
            print("严格条件未找到特征，尝试放宽显著性要求...")
            selected_features = target_data[
                (target_data['pearson_abs_corr'] >= self.correlation_threshold) &
                (target_data['pearson_p_value'] <= self.p_value_threshold)
            ]

        if len(selected_features) == 0:
            print("未找到符合条件的特征")
            print(f"可用特征的相关系数范围: {target_data['pearson_abs_corr'].min():.3f} - {target_data['pearson_abs_corr'].max():.3f}")
            print(f"可用特征的p值范围: {target_data['pearson_p_value'].min():.3f} - {target_data['pearson_p_value'].max():.3f}")
            return [], {}

        # 计算基于相关性的权重
        feature_weights = self._calculate_correlation_weights(selected_features)
        feature_names = list(feature_weights.keys())

        print(f"\n=== {target_column} 特征选择结果 ===")
        print(f"相关系数阈值: {self.correlation_threshold}")
        print(f"p值阈值: {self.p_value_threshold}")
        print(f"选中特征数量: {len(feature_names)}")

        if feature_names:
            print("选中的特征及权重:")
            for feature, weight in feature_weights.items():
                corr_row = selected_features[selected_features['weather_variable'] == feature].iloc[0]
                print(f"  {feature}: r={corr_row['pearson_correlation']:.3f}, p={corr_row['pearson_p_value']:.4f}, weight={weight:.3f}")

        return feature_names, feature_weights

    def _calculate_correlation_weights(self, selected_features: pd.DataFrame) -> Dict[str, float]:
        """
        基于相关性强度计算特征权重

        Args:
            selected_features: 选中的特征数据框

        Returns:
            Dict[str, float]: 特征名称到权重的映射
        """
        # 使用绝对相关系数作为权重基础
        abs_correlations = selected_features['pearson_abs_corr'].values
        feature_names = selected_features['weather_variable'].values

        # 方法1: 直接使用相关系数作为权重（归一化）
        # weights = abs_correlations / abs_correlations.sum()

        # 方法2: 使用相关系数的平方作为权重（强调强相关性）
        squared_correlations = abs_correlations ** 2
        weights = squared_correlations / squared_correlations.sum()

        # 方法3: 使用softmax变换（可选，增强权重差异）
        # import numpy as np
        # exp_correlations = np.exp(abs_correlations * 5)  # 5是温度参数
        # weights = exp_correlations / exp_correlations.sum()

        return dict(zip(feature_names, weights))

    def get_feature_data(self, target_column: str, feature_names: List[str],
                        feature_weights: Dict[str, float]) -> Optional[pd.DataFrame]:
        """
        获取特征数据并应用权重

        Args:
            target_column: 目标电力类型
            feature_names: 特征名称列表
            feature_weights: 特征权重字典

        Returns:
            pd.DataFrame: 包含目标变量和加权特征的数据框
        """
        if self.monthly_data is None:
            print("月度数据未加载")
            return None

        if not feature_names:
            print("没有选中的特征")
            return None

        # 创建目标列名映射，处理不同的命名方式
        target_mapping = {
            '代理购电': ['代理购电', '电网代购用户', '代购用户'],
            '居民': ['居民', '居民用电'],
            '农业': ['农业', '农业用电']
        }

        # 找到实际的目标列名
        actual_target_column = None
        possible_targets = target_mapping.get(target_column, [target_column])
        for possible_target in possible_targets:
            if possible_target in self.monthly_data.columns:
                actual_target_column = possible_target
                break

        if actual_target_column is None:
            print(f"目标列 '{target_column}' 在月度数据中不存在")
            print(f"尝试的列名: {possible_targets}")
            print(f"可用列名: {list(self.monthly_data.columns)}")
            return None

        print(f"使用目标列: {actual_target_column} (原始: {target_column})")

        # 检查特征是否存在于数据中
        available_features = []
        available_weights = {}
        for feature in feature_names:
            if feature in self.monthly_data.columns:
                available_features.append(feature)
                available_weights[feature] = feature_weights.get(feature, 1.0)
            else:
                print(f"警告: 特征 '{feature}' 在月度数据中不存在")

        if not available_features:
            print("没有可用的特征数据")
            return None

        # 构建包含目标变量和特征的数据框
        columns = [actual_target_column] + available_features
        feature_data = self.monthly_data[columns].copy()

        # 重命名目标列为原始名称，保持一致性
        feature_data = feature_data.rename(columns={actual_target_column: target_column})

        # 应用权重到特征数据
        for feature in available_features:
            weight = available_weights[feature]
            # 对特征数据应用权重（标准化后应用权重）
            feature_data[feature] = self._apply_feature_weight(feature_data[feature], weight)
            print(f"特征 '{feature}' 应用权重: {weight:.3f}")

        print(f"成功获取并加权特征数据，包含{len(available_features)}个特征")
        print(f"数据范围: {feature_data.index.min()} 到 {feature_data.index.max()}")

        return feature_data

    def _apply_feature_weight(self, feature_series: pd.Series, weight: float) -> pd.Series:
        """
        对特征序列应用权重

        Args:
            feature_series: 特征数据序列
            weight: 权重值

        Returns:
            pd.Series: 加权后的特征序列
        """
        # 检查输入数据
        if len(feature_series) == 0:
            return feature_series

        # 处理单个值的情况
        if len(feature_series) == 1:
            # 对于单个值，直接应用权重而不进行标准化
            return feature_series * weight

        # 检查标准差是否为零（所有值相同）
        feature_std = feature_series.std()
        if feature_std == 0 or pd.isna(feature_std):
            # 如果标准差为零，直接应用权重
            return feature_series * weight

        # 方法1: 直接乘以权重（简单有效）
        return feature_series * weight

        # 方法2: 标准化后应用权重（可选，用于多值序列）
        # standardized = (feature_series - feature_series.mean()) / feature_std
        # weighted = standardized * weight
        # return weighted


class ARIMAMonthlyPredictor:
    """ARIMA/SARIMAX月级别预测器"""

    def __init__(self, data_path=None, target_column='代理购电', model_type='ARIMA',
                 use_external_regressors=True, correlation_threshold=0.3, p_value_threshold=0.05,
                 results_dir='ARIMA/results', default_data_path='data/龙泉代理购电.xlsx',
                 correlation_results_pattern: Optional[str] = None,
                 monthly_data_patterns: Optional[List[str]] = None,
                 weather_data_paths: Optional[List[str]] = None):
        """
        初始化预测器

        Args:
            data_path: 数据文件路径
            target_column: 目标预测列名
            model_type: 模型类型，'ARIMA' 或 'SARIMAX'
            use_external_regressors: 是否使用外部回归变量
            correlation_threshold: 相关系数阈值
            p_value_threshold: p值阈值
            results_dir: 结果保存目录
            default_data_path: 默认数据文件路径
            correlation_results_pattern: 相关性分析结果文件路径模式
            monthly_data_patterns: 月度数据文件路径模式列表
            weather_data_paths: 天气数据文件路径列表
        """
        self.data_path = data_path
        self.target_column = target_column
        self.model_type = model_type.upper()
        self.use_external_regressors = use_external_regressors
        self.correlation_threshold = correlation_threshold
        self.p_value_threshold = p_value_threshold
        self.results_dir = results_dir
        self.default_data_path = default_data_path

        # 配置文件路径
        self.correlation_results_pattern = correlation_results_pattern
        self.monthly_data_patterns = monthly_data_patterns
        self.weather_data_paths = weather_data_paths or [
            'data/丽水市_月度统计_201901_202508.csv',
            'data/丽水市_月度天气数据_20250717_171422.xlsx',
            '..data/丽水市_月度天气数据_20250717_171422.xlsx',
            '.data/丽水市_月度天气数据_20250717_171422.xlsx'
        ]

        # 数据相关属性
        self.monthly_data = None
        self.external_regressors = None
        self.selected_features = []
        self.feature_weights = {}

        # 模型相关属性
        self.model = None
        self.fitted_model = None

        # 特征选择器
        self.feature_selector = None

        # 验证模型类型
        if self.model_type not in ['ARIMA', 'SARIMAX']:
            raise ValueError("model_type必须是'ARIMA'或'SARIMAX'")

        # 创建结果目录
        os.makedirs(self.results_dir, exist_ok=True)

        # 打印配置信息
        self._print_configuration()

    def _print_configuration(self):
        """打印当前配置信息"""
        print("\n=== ARIMA预测器配置 ===")
        print(f"模型类型: {self.model_type}")
        print(f"目标变量: {self.target_column}")
        print(f"使用外部回归变量: {self.use_external_regressors}")
        if self.use_external_regressors:
            print(f"相关系数阈值: {self.correlation_threshold}")
            print(f"p值阈值: {self.p_value_threshold}")
        print(f"结果保存目录: {self.results_dir}")
        print("=" * 30)

    def get_model_info(self):
        """获取模型信息"""
        info = {
            'model_type': self.model_type,
            'target_column': self.target_column,
            'use_external_regressors': self.use_external_regressors,
            'correlation_threshold': self.correlation_threshold,
            'p_value_threshold': self.p_value_threshold,
            'selected_features': self.selected_features,
            'feature_weights': self.feature_weights,
            'data_range': None,
            'external_regressors_available': self.external_regressors is not None
        }

        if self.monthly_data is not None:
            info['data_range'] = {
                'start': self.monthly_data.index.min().strftime('%Y-%m'),
                'end': self.monthly_data.index.max().strftime('%Y-%m'),
                'periods': len(self.monthly_data)
            }

        return info

    def load_and_prepare_data(self, data_path=None):
        """加载并准备月度数据，包括外部回归变量"""
        if data_path:
            self.data_path = data_path

        if not self.data_path:
            # 使用配置的默认数据文件路径
            self.data_path = self.default_data_path

        print(f"正在加载数据: {self.data_path}")

        # 读取数据
        df = pd.read_excel(self.data_path, sheet_name='月数据')

        # 转换日期列
        df['日期'] = pd.to_datetime(df['月份'], format='%Y%m')

        # 按月汇总数据
        df['年月'] = df['日期'].dt.to_period('M')

        # 设置日期为索引
        df.set_index('日期', inplace=True)
        self.monthly_data = df[self.target_column]

        print(f"月度数据准备完成，共{len(self.monthly_data)}个月的数据")
        print(f"数据范围: {self.monthly_data.index.min()} 到 {self.monthly_data.index.max()}")

        # 如果启用外部回归变量，尝试加载相关特征
        if self.use_external_regressors and self.model_type == 'SARIMAX':
            self._load_external_regressors()

        return self.monthly_data

    def _load_external_regressors(self):
        """加载外部回归变量"""
        print("\n=== 加载外部回归变量 ===")

        try:
            # 初始化特征选择器，传递配置的文件路径
            self.feature_selector = CorrelationFeatureSelector(
                correlation_threshold=self.correlation_threshold,
                p_value_threshold=self.p_value_threshold,
                correlation_results_pattern=self.correlation_results_pattern,
                monthly_data_patterns=self.monthly_data_patterns
            )

            # 加载相关性分析结果和月度数据
            if not self.feature_selector.load_correlation_results():
                print("无法加载相关性分析结果，将使用标准SARIMAX模型")
                self.use_external_regressors = False
                return

            if not self.feature_selector.load_monthly_data():
                print("无法加载月度聚合数据，将使用标准SARIMAX模型")
                self.use_external_regressors = False
                return

            # 选择特征并获取权重
            self.selected_features, self.feature_weights = self.feature_selector.get_selected_features(self.target_column)

            if not self.selected_features:
                print("未找到符合条件的外部回归变量，将使用标准SARIMAX模型")
                self.use_external_regressors = False
                return

            # 获取加权特征数据
            feature_data = self.feature_selector.get_feature_data(self.target_column, self.selected_features, self.feature_weights)

            if feature_data is None:
                print("无法获取特征数据，将使用标准SARIMAX模型")
                self.use_external_regressors = False
                return

            # 对齐数据（确保时间范围一致）
            common_dates = self.monthly_data.index.intersection(feature_data.index)
            if len(common_dates) == 0:
                print("目标数据与特征数据时间范围不重叠，将使用标准SARIMAX模型")
                self.use_external_regressors = False
                return

            # 更新数据为重叠的时间范围
            self.monthly_data = self.monthly_data.loc[common_dates]
            self.external_regressors = feature_data.loc[common_dates, self.selected_features]

            print(f"成功加载{len(self.selected_features)}个外部回归变量")
            print(f"对齐后的数据范围: {common_dates.min()} 到 {common_dates.max()}")
            print(f"外部回归变量: {self.selected_features}")

        except Exception as e:
            print(f"加载外部回归变量时出错: {e}")
            print("将使用标准SARIMAX模型")
            self.use_external_regressors = False
            self.external_regressors = None
            self.selected_features = []
    
    def check_stationarity(self, series, title="时间序列"):
        """检查时间序列的平稳性"""
        print(f"\n=== {title} 平稳性检验 ===")
        
        # ADF检验
        result = adfuller(series.dropna())
        print(f'ADF统计量: {result[0]:.6f}')
        print(f'p值: {result[1]:.6f}')
        print(f'临界值:')
        for key, value in result[4].items():
            print(f'\t{key}: {value:.3f}')
            
        if result[1] <= 0.05:
            print("结论: 序列是平稳的 (p <= 0.05)")
            return True
        else:
            print("结论: 序列不平稳 (p > 0.05)")
            return False
    
    def plot_series_analysis(self, series, title="时间序列分析"):
        """绘制时间序列分析图"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle(f'{title} - {self.target_column}', fontsize=16)
        
        # 原始序列
        axes[0, 0].plot(series.index, series.values)
        axes[0, 0].set_title('原始时间序列')
        axes[0, 0].set_ylabel('数值')
        
        # ACF图
        plot_acf(series.dropna(), ax=axes[0, 1], lags=min(20, len(series)//4))
        axes[0, 1].set_title('自相关函数 (ACF)')
        
        # PACF图
        plot_pacf(series.dropna(), ax=axes[1, 0], lags=min(20, len(series)//4))
        axes[1, 0].set_title('偏自相关函数 (PACF)')
        
        # 一阶差分
        diff_series = series.diff().dropna()
        axes[1, 1].plot(diff_series.index, diff_series.values)
        axes[1, 1].set_title('一阶差分序列')
        axes[1, 1].set_ylabel('差分值')
        
        plt.tight_layout()
        
        # 保存图片
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{self.results_dir}/series_analysis_{self.target_column}_{timestamp}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"序列分析图已保存: {filename}")
        
        return fig
    
    def seasonal_decomposition(self, series):
        """季节性分解"""
        if len(series) < 24:  # 至少需要2年的月度数据
            print("数据点不足，跳过季节性分解")
            return None
            
        print("\n=== 季节性分解 ===")
        
        # 进行季节性分解
        decomposition = seasonal_decompose(series, model='additive', period=12)
        
        # 绘制分解图
        fig, axes = plt.subplots(4, 1, figsize=(15, 12))
        fig.suptitle(f'季节性分解 - {self.target_column}', fontsize=16)
        
        decomposition.observed.plot(ax=axes[0], title='原始序列')
        decomposition.trend.plot(ax=axes[1], title='趋势')
        decomposition.seasonal.plot(ax=axes[2], title='季节性')
        decomposition.resid.plot(ax=axes[3], title='残差')
        
        plt.tight_layout()
        
        # 保存图片
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{self.results_dir}/seasonal_decomposition_{self.target_column}_{timestamp}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"季节性分解图已保存: {filename}")
        
        return decomposition

    def auto_model_order(self, series, max_p=5, max_d=2, max_q=5, max_P=2, max_D=1, max_Q=2, m=12):
        """自动选择模型参数"""
        if self.model_type == 'ARIMA':
            print("\n=== 自动选择ARIMA参数 ===")
        else:
            print("\n=== 自动选择SARIMAX参数 ===")
            if self.use_external_regressors and self.external_regressors is not None:
                print(f"使用外部回归变量: {self.selected_features}")

        best_aic = float('inf')
        best_order = None
        best_seasonal_order = None
        best_model = None

        # 准备外部回归变量
        exog_data = None
        if self.model_type == 'SARIMAX' and self.use_external_regressors and self.external_regressors is not None:
            exog_data = self.external_regressors

        # 网格搜索最优参数
        if self.model_type == 'ARIMA':
            # ARIMA模型参数搜索
            for p in range(max_p + 1):
                for d in range(max_d + 1):
                    for q in range(max_q + 1):
                        try:
                            model = ARIMA(series, order=(p, d, q))
                            fitted_model = model.fit()

                            if fitted_model.aic < best_aic:
                                best_aic = fitted_model.aic
                                best_order = (p, d, q)
                                best_seasonal_order = None
                                best_model = fitted_model

                        except Exception:
                            continue

            if best_order:
                print(f"最优ARIMA参数: {best_order}")
                print(f"最优AIC值: {best_aic:.2f}")
            else:
                print("未找到合适的ARIMA参数，使用默认参数 (1,1,1)")
                best_order = (1, 1, 1)
                best_seasonal_order = None

        else:
            # SARIMAX模型参数搜索
            # 为了效率，先固定季节性参数，搜索ARIMA部分
            for p in range(max_p + 1):
                for d in range(max_d + 1):
                    for q in range(max_q + 1):
                        try:
                            model = SARIMAX(series, order=(p, d, q), seasonal_order=(1, 1, 1, m), exog=exog_data)
                            fitted_model = model.fit(disp=False)

                            if fitted_model.aic < best_aic:
                                best_aic = fitted_model.aic
                                best_order = (p, d, q)
                                best_seasonal_order = (1, 1, 1, m)
                                best_model = fitted_model

                        except Exception:
                            continue

            # 使用找到的最佳ARIMA参数，搜索季节性参数
            if best_order:
                for P in range(max_P + 1):
                    for D in range(max_D + 1):
                        for Q in range(max_Q + 1):
                            try:
                                model = SARIMAX(series, order=best_order, seasonal_order=(P, D, Q, m), exog=exog_data)
                                fitted_model = model.fit(disp=False)

                                if fitted_model.aic < best_aic:
                                    best_aic = fitted_model.aic
                                    best_seasonal_order = (P, D, Q, m)
                                    best_model = fitted_model

                            except Exception:
                                continue

            if best_order and best_seasonal_order:
                print(f"最优SARIMAX参数: ARIMA{best_order} × 季节性{best_seasonal_order}")
                if exog_data is not None:
                    print(f"外部回归变量数量: {exog_data.shape[1]}")
                print(f"最优AIC值: {best_aic:.2f}")
            else:
                print("未找到合适的SARIMAX参数，使用默认参数 ARIMA(1,1,1)×(1,1,1,12)")
                best_order = (1, 1, 1)
                best_seasonal_order = (1, 1, 1, m)

        return best_order, best_seasonal_order, best_model

    def fit_model(self, order=None, seasonal_order=None):
        """拟合模型"""
        if self.monthly_data is None:
            raise ValueError("请先加载数据")

        print(f"\n=== 拟合{self.model_type}模型 ===")

        # 准备外部回归变量
        exog_data = None
        if self.model_type == 'SARIMAX' and self.use_external_regressors and self.external_regressors is not None:
            exog_data = self.external_regressors
            print(f"使用外部回归变量: {self.selected_features}")

        # 如果没有指定参数，自动选择
        if order is None:
            order, seasonal_order, self.fitted_model = self.auto_model_order(self.monthly_data)

        # 拟合模型
        if self.fitted_model is None:
            if self.model_type == 'ARIMA':
                self.model = ARIMA(self.monthly_data, order=order)
                self.fitted_model = self.model.fit()
            else:  # SARIMAX
                if seasonal_order is None:
                    seasonal_order = (1, 1, 1, 12)  # 默认季节性参数
                self.model = SARIMAX(self.monthly_data, order=order, seasonal_order=seasonal_order, exog=exog_data)
                self.fitted_model = self.model.fit(disp=False)

        # 打印模型摘要
        print(self.fitted_model.summary())

        # 模型诊断
        self.model_diagnostics()

        return self.fitted_model

    def model_diagnostics(self):
        """模型诊断"""
        print("\n=== 模型诊断 ===")

        # 残差分析
        residuals = self.fitted_model.resid

        # Ljung-Box检验
        lb_test = acorr_ljungbox(residuals, lags=min(10, len(residuals)//4), return_df=True)
        print("Ljung-Box检验结果:")
        print(lb_test)

        # 绘制残差诊断图
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('ARIMA模型诊断', fontsize=16)

        # 残差时间序列图
        axes[0, 0].plot(residuals.index, residuals.values)
        axes[0, 0].set_title('残差时间序列')
        axes[0, 0].set_ylabel('残差')

        # 残差直方图
        axes[0, 1].hist(residuals.dropna(), bins=20, alpha=0.7)
        axes[0, 1].set_title('残差分布')
        axes[0, 1].set_xlabel('残差值')

        # 残差ACF
        plot_acf(residuals.dropna(), ax=axes[1, 0], lags=min(20, len(residuals)//4))
        axes[1, 0].set_title('残差自相关')

        # Q-Q图
        from scipy import stats
        stats.probplot(residuals.dropna(), dist="norm", plot=axes[1, 1])
        axes[1, 1].set_title('Q-Q图')

        plt.tight_layout()

        # 保存诊断图
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{self.results_dir}/model_diagnostics_{self.target_column}_{timestamp}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"模型诊断图已保存: {filename}")

        return fig

    def predict_future(self, periods=12, confidence_level=0.95):
        """预测未来数据"""
        if self.fitted_model is None:
            raise ValueError("请先拟合模型")

        print(f"\n=== 预测未来{periods}个月 ===")

        # 准备外部回归变量用于预测
        exog_forecast = None
        if (self.model_type == 'SARIMAX' and self.use_external_regressors and
            self.external_regressors is not None and len(self.selected_features) > 0):

            print("准备外部回归变量用于预测...")
            exog_forecast = self._prepare_forecast_exog(periods)

            if exog_forecast is None:
                print("警告: 无法获取预测期间的外部回归变量，将使用最后已知值进行预测")
                # 使用最后已知的外部回归变量值进行预测
                last_exog_values = self.external_regressors.iloc[-1:].values
                exog_forecast = np.tile(last_exog_values, (periods, 1))
                exog_forecast = pd.DataFrame(exog_forecast, columns=self.selected_features)

        # 进行预测
        if exog_forecast is not None:
            forecast = self.fitted_model.forecast(steps=periods, exog=exog_forecast)
            conf_int = self.fitted_model.get_forecast(steps=periods, exog=exog_forecast).conf_int(alpha=1-confidence_level)
            print(f"使用{len(self.selected_features)}个外部回归变量进行预测")
        else:
            forecast = self.fitted_model.forecast(steps=periods)
            conf_int = self.fitted_model.get_forecast(steps=periods).conf_int(alpha=1-confidence_level)

        # 创建预测日期
        last_date = self.monthly_data.index[-1]
        future_dates = pd.date_range(
            start=last_date + relativedelta(months=1),
            periods=periods,
            freq='MS'
        )

        # 创建预测结果DataFrame
        predictions_df = pd.DataFrame({
            '预测值': forecast.values,
            '下限': conf_int.iloc[:, 0].values,
            '上限': conf_int.iloc[:, 1].values
        }, index=future_dates)

        print("预测结果:")
        print(predictions_df)

        # 保存预测结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_suffix = f"_with_exog" if exog_forecast is not None else ""
        filename = f"{self.results_dir}/arima_predictions_{self.target_column}_{periods}months{model_suffix}_{timestamp}.csv"
        predictions_df.to_csv(filename)
        print(f"预测结果已保存: {filename}")

        return predictions_df

    def _prepare_forecast_exog(self, periods):
        """准备预测期间的外部回归变量，优先使用浙江省月度天气数据"""
        try:
            print("准备外部回归变量用于预测...")

            # 获取最新的月度数据以推断未来的外部回归变量
            if self.feature_selector is None or self.feature_selector.monthly_data is None:
                return None

            # 获取最后已知的外部回归变量值
            last_date = self.monthly_data.index[-1]

            # 创建未来日期
            future_dates = pd.date_range(
                start=last_date + relativedelta(months=1),
                periods=periods,
                freq='MS'
            )

            print(f"准备生成未来{periods}个月的外部回归变量 ({future_dates[0].strftime('%Y-%m')} 到 {future_dates[-1].strftime('%Y-%m')})")

            # 尝试从浙江省月度天气数据文件中加载未来天气数据
            future_weather_data = self._load_future_weather_data(future_dates)

            if future_weather_data is not None:
                print("使用浙江省月度天气数据文件中的未来天气数据")
                return self._prepare_exog_from_weather_data(future_weather_data, future_dates)
            else:
                print("使用历史同期平均值生成外部回归变量")
                return self._prepare_exog_from_historical_average(future_dates)

        except Exception as e:
            print(f"生成外部回归变量预测值时出错: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _load_future_weather_data(self, future_dates):
        """从浙江省月度天气数据文件中加载未来天气数据"""
        try:
            # 使用配置的天气数据文件路径
            weather_data = None
            for path in self.weather_data_paths:
                try:
                    _, ext = os.path.splitext(path)
                    if ext.lower() == '.csv':
                        weather_data = pd.read_csv(path)
                    elif ext.lower() in ('.xls', '.xlsx'):
                        weather_data = pd.read_excel(path) 
                    print(f"成功加载浙江省天气数据: {path}")
                    break
                except FileNotFoundError:
                    continue

            if weather_data is None:
                print("未找到浙江省月度天气数据文件")
                return None

            # 处理日期列
            if '年月' in weather_data.columns:
                weather_data['日期'] = pd.to_datetime(weather_data['年月'], format='%Y-%m')
                weather_data.set_index('日期', inplace=True)

            # 检查是否有未来日期的数据
            available_future_data = {}
            for future_date in future_dates:
                if future_date in weather_data.index:
                    available_future_data[future_date] = weather_data.loc[future_date]
                    print(f"找到未来天气数据: {future_date.strftime('%Y-%m')}")

            return available_future_data if available_future_data else None

        except Exception as e:
            print(f"加载浙江省天气数据时出错: {e}")
            return None

    def _prepare_exog_from_weather_data(self, future_weather_data, future_dates):
        """使用实际天气数据准备外部回归变量"""
        try:
            forecast_exog = []

            # 创建特征名称映射
            feature_mapping = {
                '平均高温': '平均高温',
                '平均低温': '平均低温',
                '平均AQI': '平均AQI',
                '总降水量': '总降水量',
                '降水量': '总降水量',
                '高温_数值': '平均高温',
                '低温_数值': '平均低温',
                'AQI': '平均AQI'
            }

            for future_date in future_dates:
                if future_date in future_weather_data:
                    # 使用实际天气数据
                    weather_row = future_weather_data[future_date]
                    weighted_values = []

                    for feature in self.selected_features:
                        # 尝试映射特征名称
                        mapped_feature = feature_mapping.get(feature, feature)

                        if mapped_feature in weather_row:
                            actual_value = weather_row[mapped_feature]
                            weight = self.feature_weights.get(feature, 1.0)
                            weighted_value = self.feature_selector._apply_feature_weight(
                                pd.Series([actual_value]), weight
                            ).iloc[0]
                            weighted_values.append(weighted_value)
                            print(f"使用实际天气数据: {future_date.strftime('%Y-%m')} - {feature}: {actual_value}")
                        else:
                            # 如果找不到对应特征，使用历史平均值
                            historical_avg = self._get_historical_average_for_feature(feature, future_date.month)
                            weight = self.feature_weights.get(feature, 1.0)
                            weighted_value = self.feature_selector._apply_feature_weight(
                                pd.Series([historical_avg]), weight
                            ).iloc[0]
                            weighted_values.append(weighted_value)
                            print(f"特征 {feature} 未找到，使用历史平均值: {historical_avg}")

                    forecast_exog.append(weighted_values)
                else:
                    # 如果没有实际数据，使用历史平均值
                    weighted_values = []
                    for feature in self.selected_features:
                        historical_avg = self._get_historical_average_for_feature(feature, future_date.month)
                        weight = self.feature_weights.get(feature, 1.0)
                        weighted_value = self.feature_selector._apply_feature_weight(
                            pd.Series([historical_avg]), weight
                        ).iloc[0]
                        weighted_values.append(weighted_value)

                    forecast_exog.append(weighted_values)
                    print(f"使用历史同期平均值: {future_date.strftime('%Y-%m')}")

            exog_forecast = pd.DataFrame(forecast_exog,
                                       columns=self.selected_features,
                                       index=future_dates)

            print(f"成功生成{len(future_dates)}个月的外部回归变量预测值")
            return exog_forecast

        except Exception as e:
            print(f"使用天气数据准备外部回归变量时出错: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _prepare_exog_from_historical_average(self, future_dates):
        """使用历史同期平均值准备外部回归变量"""
        try:
            monthly_data = self.feature_selector.monthly_data
            forecast_exog = []

            for future_date in future_dates:
                month = future_date.month
                # 获取历史同月份的数据
                historical_same_month = monthly_data[monthly_data.index.month == month]

                if len(historical_same_month) > 0:
                    # 使用同月份历史数据的平均值
                    month_values = historical_same_month[self.selected_features].mean()
                else:
                    # 如果没有同月份数据，使用整体平均值
                    month_values = monthly_data[self.selected_features].mean()

                # 应用权重到预测的外部回归变量
                weighted_values = []
                for feature in self.selected_features:
                    weight = self.feature_weights.get(feature, 1.0)
                    weighted_value = self.feature_selector._apply_feature_weight(
                        pd.Series([month_values[feature]]), weight
                    ).iloc[0]
                    weighted_values.append(weighted_value)

                forecast_exog.append(weighted_values)

            exog_forecast = pd.DataFrame(forecast_exog,
                                       columns=self.selected_features,
                                       index=future_dates)

            print(f"成功生成{len(future_dates)}个月的外部回归变量预测值")
            return exog_forecast

        except Exception as e:
            print(f"使用历史平均值准备外部回归变量时出错: {e}")
            return None

    def _get_historical_average_for_feature(self, feature, month):
        """获取指定特征在指定月份的历史平均值"""
        try:
            monthly_data = self.feature_selector.monthly_data
            historical_same_month = monthly_data[monthly_data.index.month == month]

            if len(historical_same_month) > 0 and feature in historical_same_month.columns:
                return historical_same_month[feature].mean()
            elif feature in monthly_data.columns:
                return monthly_data[feature].mean()
            else:
                return 0.0  # 默认值
        except:
            return 0.0

    def plot_predictions(self, predictions_df, show_history_months=24):
        """绘制预测结果图"""
        print("\n=== 绘制预测结果 ===")

        # 准备历史数据（显示最近的月份）
        if show_history_months and len(self.monthly_data) > show_history_months:
            history_data = self.monthly_data.tail(show_history_months)
        else:
            history_data = self.monthly_data

        # 创建图形
        fig, ax = plt.subplots(figsize=(15, 8))

        # 绘制历史数据
        ax.plot(history_data.index, history_data.values,
                label='历史数据', color='blue', linewidth=2)

        # 绘制预测数据
        ax.plot(predictions_df.index, predictions_df['预测值'],
                label='预测值', color='red', linewidth=2, linestyle='--')

        # 绘制置信区间
        ax.fill_between(predictions_df.index,
                       predictions_df['下限'],
                       predictions_df['上限'],
                       alpha=0.3, color='red', label='置信区间')

        # 设置图形属性
        ax.set_title(f'ARIMA月度预测 - {self.target_column}', fontsize=16)
        ax.set_xlabel('日期', fontsize=12)
        ax.set_ylabel(f'{self.target_column} (万千瓦时)', fontsize=12)
        ax.legend()
        ax.grid(True, alpha=0.3)

        # 格式化x轴日期
        import matplotlib.dates as mdates
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
        plt.xticks(rotation=45)

        plt.tight_layout()

        # 保存预测图
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{self.results_dir}/arima_forecast_{self.target_column}_{timestamp}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"预测图已保存: {filename}")

        return fig

    def evaluate_model(self, test_size=6):
        """模型评估（使用最后几个月作为测试集）"""
        if len(self.monthly_data) <= test_size:
            print("数据不足，无法进行模型评估")
            return None

        # 对于SARIMAX模型，需要更多数据进行评估
        min_train_size = 24 if self.model_type == 'SARIMAX' else 12
        if len(self.monthly_data) <= min_train_size + test_size:
            print(f"数据不足，{self.model_type}模型评估需要至少{min_train_size + test_size}个月的数据")
            return None

        print(f"\n=== 模型评估（测试集大小：{test_size}个月）===")

        # 分割训练集和测试集
        train_data = self.monthly_data[:-test_size]
        test_data = self.monthly_data[-test_size:]

        print(f"训练集大小: {len(train_data)}个月")
        print(f"测试集大小: {len(test_data)}个月")

        try:
            # 准备训练集的外部回归变量
            train_exog = None
            test_exog = None
            if (self.model_type == 'SARIMAX' and self.use_external_regressors and
                self.external_regressors is not None):
                train_exog = self.external_regressors[:-test_size]
                test_exog = self.external_regressors[-test_size:]
                print(f"使用{len(self.selected_features)}个外部回归变量进行评估")

            # 在训练集上重新选择参数并拟合模型
            if self.model_type == 'ARIMA':
                # 为训练集重新选择ARIMA参数
                # 临时保存原始数据
                original_data = self.monthly_data
                original_exog = self.external_regressors

                # 设置训练数据
                self.monthly_data = train_data
                self.external_regressors = None  # ARIMA不使用外部回归变量

                best_order, _, temp_fitted = self.auto_model_order(train_data, max_p=3, max_d=2, max_q=3)
                if temp_fitted is None:
                    temp_model = ARIMA(train_data, order=best_order)
                    temp_fitted = temp_model.fit()

                # 恢复原始数据
                self.monthly_data = original_data
                self.external_regressors = original_exog

            else:  # SARIMAX
                # 为训练集重新选择SARIMAX参数，使用更保守的参数范围
                # 临时保存原始数据
                original_data = self.monthly_data
                original_exog = self.external_regressors

                # 设置训练数据
                self.monthly_data = train_data
                self.external_regressors = train_exog

                best_order, best_seasonal_order, temp_fitted = self.auto_model_order(
                    train_data, max_p=2, max_d=1, max_q=2, max_P=1, max_D=1, max_Q=1
                )
                if temp_fitted is None:
                    temp_model = SARIMAX(train_data,
                                       order=best_order,
                                       seasonal_order=best_seasonal_order,
                                       exog=train_exog)
                    temp_fitted = temp_model.fit(disp=False)

                # 恢复原始数据
                self.monthly_data = original_data
                self.external_regressors = original_exog

            print(f"评估模型参数: {temp_fitted.model.order}")
            if self.model_type == 'SARIMAX':
                print(f"季节性参数: {temp_fitted.model.seasonal_order}")
                if train_exog is not None:
                    print(f"外部回归变量: {self.selected_features}")

            # 预测测试集
            if self.model_type == 'SARIMAX' and test_exog is not None:
                forecast = temp_fitted.forecast(steps=test_size, exog=test_exog)
            else:
                forecast = temp_fitted.forecast(steps=test_size)

            # 确保预测值不为负数（对于电力数据）
            forecast = np.maximum(forecast, 0)

        except Exception as e:
            print(f"模型评估失败: {e}")
            print("尝试使用简化的模型参数...")

            # 使用简化的参数作为备选方案
            try:
                if self.model_type == 'ARIMA':
                    temp_model = ARIMA(train_data, order=(1, 1, 1))
                    temp_fitted = temp_model.fit()
                else:
                    temp_model = SARIMAX(train_data, order=(1, 1, 1), seasonal_order=(0, 1, 1, 12))
                    temp_fitted = temp_model.fit(disp=False)

                forecast = temp_fitted.forecast(steps=test_size)
                forecast = np.maximum(forecast, 0)  # 确保非负

            except Exception as e2:
                print(f"简化模型也失败: {e2}")
                return None

        # 计算评估指标
        from sklearn.metrics import mean_absolute_error, mean_squared_error

        mae = mean_absolute_error(test_data.values, forecast.values)
        mse = mean_squared_error(test_data.values, forecast.values)
        rmse = np.sqrt(mse)

        # 计算MAPE时避免除零错误，并处理异常值
        def safe_mape(actual, predicted):
            actual = np.array(actual)
            predicted = np.array(predicted)

            # 避免除零
            mask = actual != 0
            if not mask.any():
                return float('inf')

            mape_values = np.abs((actual[mask] - predicted[mask]) / actual[mask]) * 100
            # 限制MAPE的最大值，避免极端值
            mape_values = np.clip(mape_values, 0, 200)
            return np.mean(mape_values)

        mape = safe_mape(test_data.values, forecast.values)

        # 计算相对误差
        mean_actual = np.mean(test_data.values)
        relative_mae = (mae / mean_actual) * 100 if mean_actual != 0 else float('inf')

        print(f"平均绝对误差 (MAE): {mae:,.2f}")
        print(f"均方误差 (MSE): {mse:,.2f}")
        print(f"均方根误差 (RMSE): {rmse:,.2f}")
        print(f"平均绝对百分比误差 (MAPE): {mape:.2f}%")
        print(f"相对平均绝对误差: {relative_mae:.2f}%")

        # 显示预测值和实际值的对比
        print(f"\n预测值与实际值对比:")
        for i, (actual, pred) in enumerate(zip(test_data.values, forecast.values)):
            date_str = test_data.index[i].strftime('%Y-%m')
            print(f"{date_str}: 实际={actual:,.0f}, 预测={pred:,.0f}, 误差={abs(actual-pred):,.0f}")

        # 检查预测值是否合理
        if np.any(forecast.values < 0):
            print("警告: 发现负数预测值，已调整为0")

        if mape > 50:
            print("警告: MAPE过高，模型可能不适合当前数据")
            print("建议: 1) 增加训练数据量 2) 尝试不同的模型参数 3) 检查数据质量")

        # 绘制评估结果
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

        # 上图：时间序列对比
        ax1.plot(train_data.index, train_data.values, label='训练数据', color='blue', alpha=0.7)
        ax1.plot(test_data.index, test_data.values, label='实际值', color='green', marker='o', linewidth=2)
        ax1.plot(test_data.index, forecast.values, label='预测值', color='red', marker='s', linewidth=2)

        ax1.set_title(f'{self.model_type}模型评估 - {self.target_column}', fontsize=14)
        ax1.set_xlabel('日期')
        ax1.set_ylabel(f'{self.target_column} (千瓦时)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 下图：预测误差
        errors = test_data.values - forecast.values
        ax2.bar(range(len(errors)), errors, alpha=0.7,
                color=['red' if e > 0 else 'blue' for e in errors])
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax2.set_title('预测误差 (实际值 - 预测值)')
        ax2.set_xlabel('测试样本')
        ax2.set_ylabel('误差')
        ax2.grid(True, alpha=0.3)

        # 设置x轴标签为日期
        date_labels = [date.strftime('%Y-%m') for date in test_data.index]
        ax2.set_xticks(range(len(date_labels)))
        ax2.set_xticklabels(date_labels, rotation=45)

        plt.tight_layout()

        # 保存评估图
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{self.results_dir}/{self.model_type.lower()}_evaluation_{self.target_column}_{timestamp}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"评估图已保存: {filename}")

        # 保存评估结果
        eval_results = {
            'Model_Type': self.model_type,
            'Test_Size': test_size,
            'MAE': mae,
            'MSE': mse,
            'RMSE': rmse,
            'MAPE': mape,
        }

        eval_df = pd.DataFrame([eval_results])
        eval_filename = f"{self.results_dir}/{self.model_type.lower()}_evaluation_metrics_{self.target_column}_{timestamp}.csv"
        eval_df.to_csv(eval_filename, index=False)
        print(f"评估指标已保存: {eval_filename}")

        return eval_results

    def run_complete_analysis(self, prediction_months=12, evaluation=True):
        """运行完整的ARIMA分析流程"""
        print("=" * 60)
        print(f"开始ARIMA月度预测分析 - {self.target_column}")
        print("=" * 60)

        # 1. 加载数据
        self.load_and_prepare_data()

        # 2. 数据探索性分析
        print("\n步骤1: 数据探索性分析")
        self.plot_series_analysis(self.monthly_data)
        self.check_stationarity(self.monthly_data)

        # 3. 季节性分解
        print("\n步骤2: 季节性分解")
        self.seasonal_decomposition(self.monthly_data)

        # 4. 拟合模型
        print(f"\n步骤3: 拟合{self.model_type}模型")
        self.fit_model()

        # 5. 模型评估
        if evaluation and len(self.monthly_data) > 12:
            print("\n步骤4: 模型评估")
            self.evaluate_model()

        # 6. 未来预测
        print(f"\n步骤5: 预测未来{prediction_months}个月")
        predictions = self.predict_future(periods=prediction_months)

        # 7. 绘制预测结果
        print("\n步骤6: 绘制预测结果")
        self.plot_predictions(predictions)

        print("\n" + "=" * 60)
        print(f"{self.model_type}分析完成！")
        print("=" * 60)

        return predictions


def main():
    """主函数 - 演示时间序列预测器的使用"""

    # 支持的目标列
    target_columns = ['代理购电', '居民', '农业']

    print("时间序列月度电力预测系统")
    print("=" * 50)

    # 选择预测目标
    print("可用的预测目标:")
    for i, col in enumerate(target_columns, 1):
        print(f"{i}. {col}")

    try:
        choice = input(f"请选择预测目标 (1-{len(target_columns)}, 默认为1): ").strip()
        if not choice:
            choice = "1"
        target_column = target_columns[int(choice) - 1]
    except (ValueError, IndexError):
        print("无效选择，使用默认目标：代理购电")
        target_column = "代理购电"

    # 选择模型类型
    print("\n选择模型类型:")
    print("1. ARIMA (自回归积分滑动平均)")
    print("2. SARIMAX (季节性ARIMA)")
    print("3. SARIMAX with External Regressors (带外部回归变量的季节性ARIMA)")

    try:
        model_choice = input("请选择模型类型 (1-3, 默认为3): ").strip()
        if not model_choice or model_choice == "3":
            model_type = "SARIMAX"
            use_external_regressors = True
        elif model_choice == "1":
            model_type = "ARIMA"
            use_external_regressors = False
        elif model_choice == "2":
            model_type = "SARIMAX"
            use_external_regressors = False
        else:
            print("无效选择，使用默认模型：SARIMAX with External Regressors")
            model_type = "SARIMAX"
            use_external_regressors = True
    except ValueError:
        print("无效输入，使用默认模型：SARIMAX with External Regressors")
        model_type = "SARIMAX"
        use_external_regressors = True

    # 如果选择使用外部回归变量，询问相关参数
    correlation_threshold = 0.3
    p_value_threshold = 0.05

    if use_external_regressors:
        print("\n外部回归变量配置:")
        try:
            corr_input = input(f"相关系数阈值 (默认{correlation_threshold}): ").strip()
            if corr_input:
                correlation_threshold = float(corr_input)
        except ValueError:
            print(f"无效输入，使用默认值：{correlation_threshold}")

        try:
            p_input = input(f"p值阈值 (默认{p_value_threshold}): ").strip()
            if p_input:
                p_value_threshold = float(p_input)
        except ValueError:
            print(f"无效输入，使用默认值：{p_value_threshold}")

    # 预测月数
    try:
        months = input("请输入预测月数 (默认为12): ").strip()
        if not months:
            months = 12
        else:
            months = int(months)
    except ValueError:
        print("无效输入，使用默认值：12个月")
        months = 12

    # 创建预测器并运行分析
    predictor = ARIMAMonthlyPredictor(
        data_path="./data/居民、农业2019-.xlsx",
        target_column=target_column,
        model_type=model_type,
        use_external_regressors=use_external_regressors,
        correlation_threshold=correlation_threshold,
        p_value_threshold=p_value_threshold,
        default_data_path="./data/居民、农业2019-.xlsx",  # 可配置的默认数据路径
        correlation_results_pattern='feature_analysis_results/result/monthly_weather_electricity_correlations_*.csv',
        monthly_data_patterns=['./data/整合数据2025_7.xlsx', './data/整合天气数据_代理购电_20250717_171422.xlsx'],
        weather_data_paths=['data/丽水市_月度统计_201901_202508.csv']
    )

    try:
        predictions = predictor.run_complete_analysis(prediction_months=months)

        print(f"\n预测结果摘要:")
        print(f"模型类型: {model_type}")
        print(f"目标变量: {target_column}")
        print(f"预测期间: {predictions.index[0].strftime('%Y-%m')} 到 {predictions.index[-1].strftime('%Y-%m')}")
        print(f"平均预测值: {predictions['预测值'].mean():.2f}")
        print(f"预测值范围: {predictions['预测值'].min():.2f} - {predictions['预测值'].max():.2f}")

        # 显示前几个月的预测结果
        print(f"\n前6个月预测详情:")
        display_predictions = predictions.head(6).copy()
        display_predictions.index = display_predictions.index.strftime('%Y-%m')
        print(display_predictions.round(2))

    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
