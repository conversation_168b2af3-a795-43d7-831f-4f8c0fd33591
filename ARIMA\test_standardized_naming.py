"""
测试ARIMA月度预测器的标准化文件命名规范
验证所有输出文件都遵循新的命名格式
"""

import sys
import os
import glob
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from arima_monthly_predictor import ARIMAMonthlyPredictor


def test_standardized_naming():
    """测试标准化文件命名规范"""
    
    print("=" * 80)
    print("测试ARIMA预测器标准化文件命名规范")
    print("=" * 80)
    
    # 清理之前的测试文件
    results_dir = "ARIMA/results/new"
    if os.path.exists(results_dir):
        old_files = glob.glob(os.path.join(results_dir, "*"))
        print(f"清理 {len(old_files)} 个旧文件...")
        for file in old_files:
            try:
                os.remove(file)
            except:
                pass
    
    # 测试不同配置的文件命名
    test_configs = [
        {
            'target_column': '代理购电',
            'model_type': 'SARIMAX',
            'use_external_regressors': True,
            'expected_type': 'proxy_purchase',
            'expected_exog': 'with_exog'
        },
        {
            'target_column': '居民',
            'model_type': 'ARIMA',
            'use_external_regressors': False,
            'expected_type': 'residential',
            'expected_exog': 'no_exog'
        },
        {
            'target_column': '农业',
            'model_type': 'SARIMAX',
            'use_external_regressors': False,
            'expected_type': 'agricultural',
            'expected_exog': 'no_exog'
        }
    ]
    
    for i, config in enumerate(test_configs, 1):
        print(f"\n{'='*60}")
        print(f"测试配置 {i}: {config['target_column']} - {config['model_type']}")
        print(f"外部回归变量: {'是' if config['use_external_regressors'] else '否'}")
        print(f"{'='*60}")
        
        try:
            # 创建预测器
            predictor = ARIMAMonthlyPredictor(
                data_path="./data/居民、农业2019-.xlsx",
                target_column=config['target_column'],
                model_type=config['model_type'],
                use_external_regressors=config['use_external_regressors'],
                correlation_threshold=0.3,
                p_value_threshold=0.05,
                default_data_path="./data/居民、农业2019-.xlsx",
                correlation_results_pattern='feature_analysis_results/result/monthly_weather_electricity_correlations_*.csv',
                monthly_data_patterns=['./data/整合数据2025_7.xlsx', './data/整合天气数据_代理购电_20250717_171422.xlsx'],
                weather_data_paths=['data/丽水市_月度统计_201901_202508.csv']
            )
            
            # 测试文件名生成
            print(f"\n文件命名测试:")
            test_file_types = ['prediction', 'evaluation', 'plot', 'model', 'diagnostics']
            
            for file_type in test_file_types:
                filename = predictor._generate_filename(file_type, 'csv' if file_type in ['prediction', 'evaluation', 'model'] else 'png')
                print(f"  {file_type:12}: {filename}")
                
                # 验证文件名格式
                parts = filename.split('-')
                if len(parts) >= 5:
                    actual_file_type = parts[0]
                    actual_electricity_type = parts[1]
                    actual_model = parts[2]
                    actual_exog = parts[3]
                    actual_timestamp = parts[4].split('.')[0]
                    
                    # 验证各部分
                    assert actual_file_type == file_type, f"文件类型不匹配: {actual_file_type} != {file_type}"
                    assert actual_electricity_type == config['expected_type'], f"用电类型不匹配: {actual_electricity_type} != {config['expected_type']}"
                    assert actual_model == config['model_type'], f"模型类型不匹配: {actual_model} != {config['model_type']}"
                    assert actual_exog == config['expected_exog'], f"外部变量标识不匹配: {actual_exog} != {config['expected_exog']}"
                    
                    # 验证时间戳格式
                    try:
                        datetime.strptime(actual_timestamp, "%Y%m%d_%H%M%S")
                        print(f"    ✓ 时间戳格式正确: {actual_timestamp}")
                    except ValueError:
                        print(f"    ✗ 时间戳格式错误: {actual_timestamp}")
                        
                    print(f"    ✓ 文件名格式验证通过")
                else:
                    print(f"    ✗ 文件名格式错误: {filename}")
            
            # 运行简短的分析以生成实际文件
            print(f"\n运行简短分析以生成实际文件...")
            
            # 只加载数据，不进行完整分析
            predictor.load_and_prepare_data()
            
            if predictor.monthly_data is not None and len(predictor.monthly_data) > 6:
                # 生成一些测试文件
                print("生成测试文件:")
                
                # 1. 测试序列分析图
                try:
                    predictor.plot_series_analysis(predictor.monthly_data)
                    print("  ✓ 序列分析图已生成")
                except Exception as e:
                    print(f"  ✗ 序列分析图生成失败: {e}")
                
                # 2. 测试季节性分解图
                try:
                    if len(predictor.monthly_data) >= 24:
                        predictor.seasonal_decomposition(predictor.monthly_data)
                        print("  ✓ 季节性分解图已生成")
                    else:
                        print("  - 数据不足，跳过季节性分解")
                except Exception as e:
                    print(f"  ✗ 季节性分解图生成失败: {e}")
                
                # 3. 测试预测结果（简化版）
                try:
                    # 拟合简单模型
                    predictor.fit_model(order=(1,1,1), seasonal_order=(0,1,1,12) if config['model_type'] == 'SARIMAX' else None)
                    
                    # 生成预测
                    predictions = predictor.predict_future(periods=3)
                    print("  ✓ 预测结果已生成")
                    
                    # 生成预测图
                    predictor.plot_predictions(predictions)
                    print("  ✓ 预测图已生成")
                    
                except Exception as e:
                    print(f"  ✗ 预测相关文件生成失败: {e}")
            
            print(f"✓ 配置 {i} 测试完成")
            
        except Exception as e:
            print(f"✗ 配置 {i} 测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    # 检查生成的文件
    print(f"\n{'='*60}")
    print("检查生成的文件")
    print(f"{'='*60}")
    
    if os.path.exists(results_dir):
        generated_files = glob.glob(os.path.join(results_dir, "*"))
        print(f"共生成 {len(generated_files)} 个文件:")
        
        for file_path in sorted(generated_files):
            filename = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            print(f"  {filename} ({file_size} bytes)")
            
            # 验证文件名格式
            if '-' in filename and len(filename.split('-')) >= 5:
                print(f"    ✓ 文件名格式符合标准")
            else:
                print(f"    ✗ 文件名格式不符合标准")
    else:
        print("未找到结果目录")
    
    print(f"\n{'='*80}")
    print("标准化文件命名规范测试完成")
    print("新的命名格式: {文件类型}-{用电类型}-{模型名称}-{外部变量标识}-{日期时间戳}")
    print("示例: prediction-proxy_purchase-SARIMAX-with_exog-20250729_143022.csv")
    print(f"{'='*80}")


if __name__ == "__main__":
    test_standardized_naming()
